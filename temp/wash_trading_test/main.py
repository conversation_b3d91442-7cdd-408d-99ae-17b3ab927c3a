#!/usr/bin/env python3
"""
对敲检测测试系统主程序
整合所有模块，提供统一的操作界面
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from test_data_generator import WashTradingTestGenerator
from unified_trader import UnifiedTrader
from trade_recorder import TradeRecorder
from test_execution_engine import TestExecutionEngine
from concurrent_execution_engine import ConcurrentExecutionEngine
from data_analyzer import WashTradingDataAnalyzer


class WashTradingTestSystem:
    """对敲检测测试系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.generator = WashTradingTestGenerator()
        self.trader = UnifiedTrader()
        self.recorder = TradeRecorder()
        self.engine = TestExecutionEngine()
        self.concurrent_engine = ConcurrentExecutionEngine()
        self.analyzer = WashTradingDataAnalyzer()
        
        print("🎯 对敲检测测试系统")
        print("=" * 60)
        print("系统功能:")
        print("• 支持 BTC、ETH、DOG 三个币种")
        print("• 测试范围: 10U-10万U")
        print("• 时间间隔: 1-120秒")
        print("• 自动记录和分析交易数据")
        print("• 生成对敲检测报告")
        print("=" * 60)
    
    def show_system_status(self):
        """显示系统状态"""
        print("\n📊 系统状态")
        print("-" * 40)
        
        # 检查数据库
        try:
            records = self.recorder.load_from_database(limit=1)
            total_records = len(self.recorder.load_from_database())
            print(f"✅ 数据库连接正常，共有 {total_records} 条交易记录")
        except Exception as e:
            print(f"❌ 数据库连接异常: {e}")
        
        # 检查交易接口
        try:
            self.trader.show_coin_ranges()
            print("✅ 交易接口正常")
        except Exception as e:
            print(f"❌ 交易接口异常: {e}")
        
        # 显示支持的币种范围
        print("\n💰 支持的币种范围:")
        for coin, config in self.trader.COIN_CONFIGS.items():
            print(f"  {config.emoji} {coin}: {config.min_usdt}-{config.max_usdt} USDT")
    
    def run_quick_test(self):
        """运行快速测试"""
        print("\n🚀 快速测试模式")
        print("-" * 40)

        try:
            # 生成3个测试用例（刚好3个币种各一个）
            test_cases = self.generator.generate_test_batch_with_position_management(3)
            print(f"生成了 {len(test_cases)} 个测试用例（3个币种各一个）")
            
            # 显示测试用例
            for i, case in enumerate(test_cases, 1):
                print(f"  {i}. {case.description}")
            
            # 确认执行
            confirm = input("\n确认执行快速测试？(y/N): ").strip().lower()
            if confirm == 'y':
                print("\n🚀 使用并发执行模式（同时开仓，按时间平仓）...")
                results = self.concurrent_engine.execute_batch_concurrent(test_cases)

                # 显示结果
                successful = sum(1 for r in results if r['success'])
                failed = len(results) - successful

                print(f"\n✅ 快速测试完成！")
                print(f"成功: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
                print(f"失败: {failed}/{len(results)} ({failed/len(results)*100:.1f}%)")

                # 显示统计
                self.recorder.print_statistics()
            else:
                print("❌ 用户取消测试")
                
        except Exception as e:
            print(f"❌ 快速测试失败: {e}")
    
    def run_full_test_suite(self):
        """运行完整测试套件"""
        print("\n🎯 完整测试套件")
        print("-" * 40)

        try:
            # 生成3000个测试用例（使用仓位管理）
            test_cases = self.generator.generate_test_batch_with_position_management(3000)
            print(f"生成了 {len(test_cases)} 个测试用例（一组一组执行）")

            # 显示统计信息
            coin_counts = {}
            for case in test_cases:
                coin_counts[case.coin] = coin_counts.get(case.coin, 0) + 1

            print("\n📊 币种分布:")
            for coin, count in coin_counts.items():
                percentage = count / len(test_cases) * 100
                print(f"  {coin}: {count} 个 ({percentage:.1f}%)")

            print("\n🔄 执行模式: 并发对敲交易（一组一组完成）")
            print("  • 每组包含: 开多头 → 开空头 → 平多头 → 平空头")
            print("  • 避免仓位叠加，确保数据准确性")
            print("⚠️  预计耗时: 1-2小时")
            print("⚠️  将产生真实的交易费用")

            # 确认执行
            confirm = input(f"\n确认执行 {len(test_cases)} 个测试？(输入 'YES' 确认): ").strip()
            if confirm == 'YES':
                print("\n🚀 使用并发执行模式（一组一组完成）...")
                results = self.concurrent_engine.execute_batch_concurrent(test_cases)

                # 显示结果
                successful = sum(1 for r in results if r['success'])
                failed = len(results) - successful

                print(f"\n✅ 完整测试完成！")
                print(f"成功: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
                print(f"失败: {failed}/{len(results)} ({failed/len(results)*100:.1f}%)")

                # 显示统计
                self.recorder.print_statistics()
            else:
                print("❌ 用户取消测试")

        except Exception as e:
            print(f"❌ 完整测试失败: {e}")
    
    def run_custom_test(self):
        """运行自定义测试"""
        print("\n⚙️  自定义测试")
        print("-" * 40)
        
        try:
            # 获取测试参数
            test_count = int(input("请输入测试数量 (1-1000): "))
            if not (1 <= test_count <= 1000):
                print("❌ 测试数量必须在1-1000之间")
                return
            
            # 币种分布
            print("\n币种分布设置 (留空使用默认分布):")
            dog_percent = input("DOG币种比例 (默认40%): ").strip()
            eth_percent = input("ETH币种比例 (默认40%): ").strip()
            btc_percent = input("BTC币种比例 (默认20%): ").strip()
            
            coin_distribution = None
            if dog_percent or eth_percent or btc_percent:
                dog_percent = float(dog_percent or 40) / 100
                eth_percent = float(eth_percent or 40) / 100
                btc_percent = float(btc_percent or 20) / 100
                
                total = dog_percent + eth_percent + btc_percent
                if abs(total - 1.0) > 0.01:
                    print("❌ 币种比例总和必须为100%")
                    return
                
                coin_distribution = {
                    'DOG': dog_percent,
                    'ETH': eth_percent,
                    'BTC': btc_percent
                }
            
            # 生成测试用例（使用仓位管理）
            test_cases = self.generator.generate_test_batch_with_position_management(test_count, coin_distribution)

            print(f"\n🔄 执行模式: 并发对敲交易（一组一组完成）")
            print("  • 每组包含: 开多头 → 开空头 → 平多头 → 平空头")

            # 执行测试（使用并发引擎）
            print("\n🚀 使用并发执行模式...")
            results = self.concurrent_engine.execute_batch_concurrent(test_cases)

            # 显示结果
            successful = sum(1 for r in results if r['success'])
            failed = len(results) - successful

            print(f"\n✅ 自定义测试完成！")
            print(f"成功: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
            print(f"失败: {failed}/{len(results)} ({failed/len(results)*100:.1f}%)")

            # 显示统计
            self.recorder.print_statistics()
            
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 自定义测试失败: {e}")
    
    def analyze_test_results(self):
        """分析测试结果"""
        print("\n📊 测试结果分析")
        print("-" * 40)
        
        try:
            # 检查是否有数据
            records = self.recorder.load_from_database(limit=1)
            if not records:
                print("❌ 没有找到测试数据，请先运行测试")
                return
            
            # 执行分析
            print("🔍 正在分析潜在对敲交易对...")
            results = self.analyzer.find_potential_wash_trading_pairs()
            
            if results:
                print(f"✅ 分析完成，找到 {len(results)} 个交易对")
                self.analyzer.print_analysis_summary()
                
                # 询问是否导出
                export = input("\n是否导出分析结果？(y/N): ").strip().lower()
                if export == 'y':
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    json_file = f"wash_trading_analysis_{timestamp}.json"
                    csv_file = f"wash_trading_analysis_{timestamp}.csv"
                    
                    self.analyzer.export_analysis_results(json_file, 'json')
                    self.analyzer.export_analysis_results(csv_file, 'csv')
                    print(f"✅ 分析结果已导出到 {json_file} 和 {csv_file}")
            else:
                print("❌ 没有找到潜在的对敲交易对")
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def view_test_records(self):
        """查看测试记录"""
        print("\n📝 测试记录查看")
        print("-" * 40)
        
        try:
            # 加载最近的记录
            records = self.recorder.load_from_database(limit=50)
            if not records:
                print("❌ 没有找到测试记录")
                return
            
            print(f"📋 最近 {len(records)} 条测试记录:")
            for i, record in enumerate(records[:10], 1):
                status = "✅" if record.trade_success else "❌"
                print(f"  {i}. {status} {record.coin} {record.usdt_amount}U "
                      f"({record.planned_wait_seconds}s) - {record.created_at[:19]}")
            
            if len(records) > 10:
                print(f"  ... 还有 {len(records) - 10} 条记录")
            
            # 显示统计
            self.recorder.print_statistics()
            
            # 询问是否导出
            export = input("\n是否导出记录？(y/N): ").strip().lower()
            if export == 'y':
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                csv_file = f"trade_records_{timestamp}.csv"
                json_file = f"trade_records_{timestamp}.json"
                
                self.recorder.export_to_csv(csv_file)
                self.recorder.export_to_json(json_file)
                print(f"✅ 记录已导出到 {csv_file} 和 {json_file}")
                
        except Exception as e:
            print(f"❌ 查看记录失败: {e}")
    
    def run(self):
        """运行主程序"""
        while True:
            print("\n🎯 对敲检测测试系统")
            print("=" * 40)
            print("1. 查看系统状态")
            print("2. 运行快速测试 (3个测试)")
            print("3. 运行完整测试套件 (3000个测试)")
            print("4. 运行自定义测试")
            print("5. 分析测试结果")
            print("6. 查看测试记录")
            print("7. 生成测试用例预览")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-7): ").strip()
            
            if choice == '0':
                print("👋 感谢使用对敲检测测试系统！")
                break
            elif choice == '1':
                self.show_system_status()
            elif choice == '2':
                self.run_quick_test()
            elif choice == '3':
                self.run_full_test_suite()
            elif choice == '4':
                self.run_custom_test()
            elif choice == '5':
                self.analyze_test_results()
            elif choice == '6':
                self.view_test_records()
            elif choice == '7':
                try:
                    count = int(input("请输入预览数量 (1-100): ") or "10")
                    if 1 <= count <= 100:
                        test_cases = self.generator.generate_test_batch(count)
                        self.generator.print_statistics(test_cases)
                        
                        print(f"\n📋 测试用例预览:")
                        for i, case in enumerate(test_cases[:min(20, count)], 1):
                            print(f"  {i}. {case.description} ({case.test_type})")
                        
                        if count > 20:
                            print(f"  ... 还有 {count - 20} 个测试用例")
                    else:
                        print("❌ 数量必须在1-100之间")
                except ValueError:
                    print("❌ 请输入有效数字")
            else:
                print("❌ 无效选择")


def main():
    """主函数"""
    try:
        system = WashTradingTestSystem()
        system.run()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")


if __name__ == "__main__":
    main()
