#!/usr/bin/env python3
"""
导出全部对敲测试数据的脚本
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trade_recorder import TradeRecorder
from data_analyzer import WashTradingDataAnalyzer


def export_all_trade_records():
    """导出全部交易记录"""
    print("🔄 正在导出全部交易记录...")
    print("=" * 50)
    
    # 初始化记录器
    recorder = TradeRecorder()
    
    try:
        # 加载全部记录
        all_records = recorder.load_from_database()
        total_count = len(all_records)
        
        if total_count == 0:
            print("❌ 数据库中没有交易记录")
            return
        
        print(f"📊 数据库中共有 {total_count} 条交易记录")
        
        # 显示统计信息
        recorder.print_statistics()
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        csv_file = f"all_trade_records_{timestamp}.csv"
        json_file = f"all_trade_records_{timestamp}.json"
        
        # 导出文件
        print(f"\n🔄 正在导出到文件...")
        recorder.export_to_csv(csv_file)
        recorder.export_to_json(json_file)
        
        print(f"\n✅ 导出完成！")
        print(f"📁 CSV文件: {csv_file}")
        print(f"📁 JSON文件: {json_file}")
        print(f"📊 总记录数: {total_count}")
        
        return csv_file, json_file, total_count
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return None, None, 0


def export_all_analysis_results():
    """导出全部分析结果"""
    print("\n🔄 正在分析并导出对敲检测结果...")
    print("=" * 50)
    
    try:
        # 初始化分析器
        analyzer = WashTradingDataAnalyzer()
        
        # 执行分析
        print("🔍 正在分析潜在对敲交易对...")
        results = analyzer.find_potential_wash_trading_pairs()
        
        if not results:
            print("❌ 没有找到潜在的对敲交易对")
            return None, None, 0
        
        print(f"✅ 分析完成，找到 {len(results)} 个潜在对敲交易对")
        
        # 显示分析摘要
        analyzer.print_analysis_summary()
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        json_file = f"all_wash_trading_analysis_{timestamp}.json"
        csv_file = f"all_wash_trading_analysis_{timestamp}.csv"
        
        # 导出分析结果
        print(f"\n🔄 正在导出分析结果...")
        analyzer.export_analysis_results(json_file, 'json')
        analyzer.export_analysis_results(csv_file, 'csv')
        
        print(f"\n✅ 分析结果导出完成！")
        print(f"📁 JSON文件: {json_file}")
        print(f"📁 CSV文件: {csv_file}")
        print(f"📊 对敲交易对数: {len(results)}")
        
        return json_file, csv_file, len(results)
        
    except Exception as e:
        print(f"❌ 分析导出失败: {e}")
        return None, None, 0


def main():
    """主函数"""
    print("🎯 对敲测试数据全量导出工具")
    print("=" * 50)
    
    while True:
        print("\n📋 选择导出内容:")
        print("1. 导出全部交易记录")
        print("2. 导出全部对敲分析结果")
        print("3. 导出全部数据（交易记录 + 分析结果）")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            export_all_trade_records()
        elif choice == '2':
            export_all_analysis_results()
        elif choice == '3':
            print("🔄 开始全量导出...")
            
            # 导出交易记录
            csv_file, json_file, record_count = export_all_trade_records()
            
            # 导出分析结果
            analysis_json, analysis_csv, analysis_count = export_all_analysis_results()
            
            # 总结
            print("\n" + "=" * 50)
            print("📊 全量导出完成总结:")
            print(f"📁 交易记录: {record_count} 条")
            if csv_file:
                print(f"   - CSV: {csv_file}")
                print(f"   - JSON: {json_file}")
            
            print(f"📁 对敲分析: {analysis_count} 个交易对")
            if analysis_json:
                print(f"   - JSON: {analysis_json}")
                print(f"   - CSV: {analysis_csv}")
            print("=" * 50)
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
